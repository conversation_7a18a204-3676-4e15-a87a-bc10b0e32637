import React from 'react';
import { Metadata } from 'next';
import Layout from '@/components/Layout';
import ToolsPageClient from '@/components/tools/ToolsPageClient';
import { apiClient } from '@/lib/api';
import { getToolListStructuredData, getBreadcrumbStructuredData } from '@/lib/seo/structuredData';

// 生成静态metadata
export const metadata: Metadata = {
  title: 'AI工具目录 - 发现最好的人工智能工具',
  description: '浏览完整的AI工具目录，发现适合您需求的人工智能工具。包含文本生成、图像创作、数据分析、自动化等各类AI工具。',
  keywords: 'AI工具目录,人工智能工具,AI工具列表,机器学习工具,深度学习工具,AI应用,自动化工具,智能工具',
  authors: [{ name: 'AI工具导航团队' }],
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/tools`,
    siteName: 'AI工具导航',
    title: 'AI工具目录 - 发现最好的人工智能工具',
    description: '浏览完整的AI工具目录，发现适合您需求的人工智能工具。包含文本生成、图像创作、数据分析、自动化等各类AI工具。',
    images: [
      {
        url: '/og-tools.jpg',
        width: 1200,
        height: 630,
        alt: 'AI工具目录 - 发现最好的人工智能工具',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI工具目录 - 发现最好的人工智能工具',
    description: '浏览完整的AI工具目录，发现适合您需求的人工智能工具。',
    images: ['/og-tools.jpg'],
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'}/tools`,
  },
};

// 服务端数据获取函数
async function getToolsData() {
  try {
    const response = await apiClient.getTools({
      status: 'published',
      limit: 100
    });

    if (response.success && response.data) {
      return {
        tools: response.data.tools,
        error: null
      };
    } else {
      return {
        tools: [],
        error: response.error || '获取工具列表失败'
      };
    }
  } catch (error) {
    console.error('Failed to fetch tools:', error);
    return {
      tools: [],
      error: '获取工具列表失败，请稍后重试'
    };
  }
}

export default async function ToolsPage() {
  const { tools, error } = await getToolsData();

  // 生成结构化数据
  const toolListStructuredData = tools.length > 0 ? getToolListStructuredData(tools) : null;
  const breadcrumbStructuredData = getBreadcrumbStructuredData([
    { name: '首页', url: '/' },
    { name: 'AI工具目录', url: '/tools' }
  ]);

  return (
    <Layout>
      {/* 结构化数据 */}
      {toolListStructuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(toolListStructuredData)
          }}
        />
      )}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbStructuredData)
        }}
      />

      {/* 面包屑导航 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <nav className="flex" aria-label="面包屑导航">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <a
                href="/"
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
              >
                首页
              </a>
            </li>
            <li>
              <div className="flex items-center">
                <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">AI工具目录</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <ToolsPageClient initialTools={tools} error={error} />
    </Layout>
  );
}
